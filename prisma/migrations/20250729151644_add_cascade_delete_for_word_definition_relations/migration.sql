-- DropFore<PERSON><PERSON>ey
ALTER TABLE "Definition" DROP CONSTRAINT "Definition_word_id_fkey";

-- DropForeignKey
ALTER TABLE "Example" DROP CONSTRAINT "Example_definition_id_fkey";

-- DropForeignKey
ALTER TABLE "Explain" DROP CONSTRAINT "Explain_definition_id_fkey";

-- AddForeignKey
ALTER TABLE "Definition" ADD CONSTRAINT "Definition_word_id_fkey" FOREIGN KEY ("word_id") REFERENCES "Word"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddFore<PERSON>Key
ALTER TABLE "Explain" ADD CONSTRAINT "Explain_definition_id_fkey" FOREIGN KEY ("definition_id") REFERENCES "Definition"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Example" ADD CONSTRAINT "Example_definition_id_fkey" FOREIGN KEY ("definition_id") REFERENCES "Definition"("id") ON DELETE CASCADE ON UPDATE CASCADE;
