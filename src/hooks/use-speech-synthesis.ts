'use client';

import { useCallback, useRef } from 'react';
import { Language } from '@prisma/client';
import { speechConfig } from '@/config';

interface UseSpeechSynthesisOptions {
	rate?: number;
	pitch?: number;
	volume?: number;
}

interface SpeechSynthesisHook {
	speak: (text: string, language: Language, options?: UseSpeechSynthesisOptions) => void;
	stop: () => void;
	isSupported: boolean;
	isSpeaking: boolean;
}

/**
 * Custom hook for speech synthesis functionality
 * Provides text-to-speech capabilities with language support
 */
export function useSpeechSynthesis(): SpeechSynthesisHook {
	const utteranceRef = useRef<SpeechSynthesisUtterance | null>(null);

	// Check if speech synthesis is supported
	const isSupported = typeof window !== 'undefined' && 'speechSynthesis' in window;

	// Check if currently speaking
	const isSpeaking = isSupported && speechSynthesis.speaking;

	/**
	 * Convert Language enum to BCP 47 language tag
	 */
	const getLanguageTag = useCallback((language: Language): string => {
		return speechConfig.languageMap[language] || speechConfig.languageMap.EN;
	}, []);

	/**
	 * Speak the given text in the specified language
	 */
	const speak = useCallback(
		(text: string, language: Language, options?: UseSpeechSynthesisOptions) => {
			if (!isSupported || !text.trim()) {
				return;
			}

			// Stop any current speech
			speechSynthesis.cancel();

			// Create new utterance
			const utterance = new SpeechSynthesisUtterance(text);
			utterance.lang = getLanguageTag(language);
			utterance.rate = options?.rate ?? speechConfig.defaultRate;
			utterance.pitch = options?.pitch ?? speechConfig.defaultPitch;
			utterance.volume = options?.volume ?? speechConfig.defaultVolume;

			// Store reference for potential cancellation
			utteranceRef.current = utterance;

			// Add event listeners for better UX
			utterance.onstart = () => {
				console.debug('Speech synthesis started');
			};

			utterance.onend = () => {
				console.debug('Speech synthesis ended');
				utteranceRef.current = null;
			};

			utterance.onerror = (event) => {
				console.error('Speech synthesis error:', event.error);
				utteranceRef.current = null;
			};

			// Start speaking
			speechSynthesis.speak(utterance);
		},
		[isSupported, getLanguageTag]
	);

	/**
	 * Stop current speech synthesis
	 */
	const stop = useCallback(() => {
		if (isSupported) {
			speechSynthesis.cancel();
			utteranceRef.current = null;
		}
	}, [isSupported]);

	return {
		speak,
		stop,
		isSupported,
		isSpeaking,
	};
}
