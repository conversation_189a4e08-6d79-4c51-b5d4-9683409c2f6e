'use client';

import { useCallback, useState } from 'react';

interface Example {
	id?: string;
	EN: string;
	VI: string;
	created_at?: Date;
	updated_at?: Date;
}

interface ExampleState {
	examples: Example[];
	initialExamplesCount: number; // Track how many initial examples we started with
	loading: boolean;
	error: string | null;
	hasMore: boolean;
}

export interface UseWordExamplesResult {
	exampleStates: Record<string, ExampleState>;
	loadMoreExamples: (wordId: string, definitionId: string) => Promise<void>;
	resetExamples: (definitionId: string) => void;
	getExampleState: (definitionId: string) => ExampleState;
	initializeExamples: (definitionId: string, initialExamples: Example[]) => void;
}

const DEFAULT_STATE: ExampleState = {
	examples: [],
	initialExamplesCount: 0,
	loading: false,
	error: null,
	hasMore: true,
};

export function useWordExamples(): UseWordExamplesResult {
	const [exampleStates, setExampleStates] = useState<Record<string, ExampleState>>({});

	const getExampleState = useCallback(
		(definitionId: string): ExampleState => {
			return exampleStates[definitionId] || DEFAULT_STATE;
		},
		[exampleStates]
	);

	const loadMoreExamples = useCallback(
		async (wordId: string, definitionId: string) => {
			if (!wordId || !definitionId) {
				console.warn('loadMoreExamples: wordId and definitionId are required');
				return;
			}

			const currentState = getExampleState(definitionId);

			// Prevent concurrent loads
			if (currentState.loading) {
				return;
			}

			// Don't load if no more examples available
			if (!currentState.hasMore) {
				return;
			}

			setExampleStates((prev) => ({
				...prev,
				[definitionId]: {
					...currentState,
					loading: true,
					error: null,
				},
			}));

			try {
				const url = new URL(`/api/words/${wordId}/examples/more`, window.location.origin);
				url.searchParams.set('definitionId', definitionId);
				// Calculate offset based on total examples we have (including initial ones)
				const totalExamplesCount = currentState.examples.length;
				url.searchParams.set('offset', totalExamplesCount.toString());
				url.searchParams.set('limit', '3');

				const response = await fetch(url.toString());

				if (!response.ok) {
					const errorData = await response
						.json()
						.catch(() => ({ error: 'Failed to parse error response' }));
					throw new Error(
						errorData.error || `HTTP ${response.status}: Failed to load more examples`
					);
				}

				const result = await response.json();

				// Validate the response structure
				if (!result || typeof result !== 'object') {
					throw new Error('Invalid response format from server');
				}

				if (!Array.isArray(result.examples)) {
					throw new Error('Response missing examples array');
				}

				// Ensure hasMore is a boolean
				const hasMore = typeof result.hasMore === 'boolean' ? result.hasMore : false;

				setExampleStates((prev) => ({
					...prev,
					[definitionId]: {
						examples: [...currentState.examples, ...result.examples],
						initialExamplesCount: currentState.initialExamplesCount,
						loading: false,
						error: null,
						hasMore,
					},
				}));
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : 'Failed to load more examples';

				console.error(
					`[useWordExamples] Error loading examples for ${definitionId}:`,
					errorMessage
				);

				setExampleStates((prev) => ({
					...prev,
					[definitionId]: {
						...currentState,
						loading: false,
						error: errorMessage,
					},
				}));
			}
		},
		[getExampleState]
	);

	const resetExamples = useCallback((definitionId: string) => {
		setExampleStates((prev) => {
			const newState = { ...prev };
			delete newState[definitionId];
			return newState;
		});
	}, []);

	const initializeExamples = useCallback((definitionId: string, initialExamples: Example[]) => {
		setExampleStates((prev) => ({
			...prev,
			[definitionId]: {
				examples: initialExamples || [],
				initialExamplesCount: (initialExamples || []).length,
				loading: false,
				error: null,
				hasMore: true,
			},
		}));
	}, []);

	return {
		exampleStates,
		loadMoreExamples,
		resetExamples,
		getExampleState,
		initializeExamples,
	};
}
