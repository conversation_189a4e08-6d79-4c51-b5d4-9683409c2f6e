import { renderHook, act } from '@testing-library/react';
import { useSpeechSynthesis } from '../use-speech-synthesis';
import { Language } from '@prisma/client';

// Mock speechSynthesis API
const mockSpeak = jest.fn();
const mockCancel = jest.fn();

Object.defineProperty(window, 'speechSynthesis', {
	writable: true,
	value: {
		speak: mockSpeak,
		cancel: mockCancel,
		speaking: false,
	},
});

// Mock SpeechSynthesisUtterance
global.SpeechSynthesisUtterance = jest.fn().mockImplementation((text) => ({
	text,
	lang: '',
	rate: 1,
	pitch: 1,
	volume: 1,
	onstart: null,
	onend: null,
	onerror: null,
}));

describe('useSpeechSynthesis', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('should return correct initial state', () => {
		const { result } = renderHook(() => useSpeechSynthesis());

		expect(result.current.isSupported).toBe(true);
		expect(result.current.isSpeaking).toBe(false);
		expect(typeof result.current.speak).toBe('function');
		expect(typeof result.current.stop).toBe('function');
	});

	it('should speak text with correct language mapping', () => {
		const { result } = renderHook(() => useSpeechSynthesis());

		act(() => {
			result.current.speak('Hello world', Language.EN);
		});

		expect(mockCancel).toHaveBeenCalledTimes(1);
		expect(SpeechSynthesisUtterance).toHaveBeenCalledWith('Hello world');
		expect(mockSpeak).toHaveBeenCalledTimes(1);

		const utterance = mockSpeak.mock.calls[0][0];
		expect(utterance.lang).toBe('en-US');
	});

	it('should map Vietnamese language correctly', () => {
		const { result } = renderHook(() => useSpeechSynthesis());

		act(() => {
			result.current.speak('Xin chào', Language.VI);
		});

		const utterance = mockSpeak.mock.calls[0][0];
		expect(utterance.lang).toBe('vi-VN');
	});

	it('should not speak empty text', () => {
		const { result } = renderHook(() => useSpeechSynthesis());

		act(() => {
			result.current.speak('', Language.EN);
		});

		expect(mockSpeak).not.toHaveBeenCalled();
	});

	it('should not speak whitespace-only text', () => {
		const { result } = renderHook(() => useSpeechSynthesis());

		act(() => {
			result.current.speak('   ', Language.EN);
		});

		expect(mockSpeak).not.toHaveBeenCalled();
	});

	it('should apply custom speech options', () => {
		const { result } = renderHook(() => useSpeechSynthesis());

		act(() => {
			result.current.speak('Test', Language.EN, {
				rate: 0.5,
				pitch: 1.5,
				volume: 0.8,
			});
		});

		const utterance = mockSpeak.mock.calls[0][0];
		expect(utterance.rate).toBe(0.5);
		expect(utterance.pitch).toBe(1.5);
		expect(utterance.volume).toBe(0.8);
	});

	it('should stop speech synthesis', () => {
		const { result } = renderHook(() => useSpeechSynthesis());

		act(() => {
			result.current.stop();
		});

		expect(mockCancel).toHaveBeenCalledTimes(1);
	});

	it('should handle unsupported environment gracefully', () => {
		// Temporarily remove speechSynthesis
		const originalSpeechSynthesis = window.speechSynthesis;
		// @ts-ignore
		delete window.speechSynthesis;

		const { result } = renderHook(() => useSpeechSynthesis());

		expect(result.current.isSupported).toBe(false);

		act(() => {
			result.current.speak('Test', Language.EN);
		});

		expect(mockSpeak).not.toHaveBeenCalled();

		// Restore speechSynthesis
		window.speechSynthesis = originalSpeechSynthesis;
	});
});
