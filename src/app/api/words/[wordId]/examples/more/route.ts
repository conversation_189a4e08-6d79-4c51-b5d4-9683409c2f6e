import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { getExamplesConfig } from '@/config';
import { auth } from '@/lib';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// Create schema dynamically based on configuration
async function createGetMoreExamplesSchema() {
	const examplesConfig = await getExamplesConfig();
	return z.object({
		definitionId: z.string().min(1, 'Definition ID is required'),
		offset: z.number().min(0, 'Offset must be non-negative').default(0),
		limit: z
			.number()
			.min(1)
			.max(examplesConfig.maxApiLimit)
			.default(examplesConfig.defaultLoadLimit),
	});
}

export async function GET(
	request: NextRequest,
	{ params }: { params: Promise<{ wordId: string }> }
) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for fetching examples.');

		const { wordId } = await params;
		const { searchParams } = new URL(request.url);

		// Use dynamic schema based on configuration
		const getMoreExamplesSchema = await createGetMoreExamplesSchema();
		const examplesConfig = await getExamplesConfig();

		const validatedData = getMoreExamplesSchema.parse({
			definitionId: searchParams.get('definitionId'),
			offset: searchParams.get('offset') ? parseInt(searchParams.get('offset')!) : 0,
			limit: searchParams.get('limit')
				? parseInt(searchParams.get('limit')!)
				: examplesConfig.defaultLoadLimit,
		});

		const { definitionId, offset, limit } = validatedData;

		const wordService = getWordService();
		const result = await wordService.getMoreExamples(wordId, definitionId, offset, limit);

		return NextResponse.json(result);
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Get more examples error:', error);
		return NextResponse.json({ error: 'Failed to fetch more examples' }, { status: 500 });
	}
}
