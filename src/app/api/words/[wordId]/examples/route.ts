import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getWordService } from '@/backend/wire';
import { getExamplesConfig } from '@/config';
import { auth } from '@/lib';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

// Create schema dynamically based on configuration
async function createAddExamplesSchema() {
	const examplesConfig = await getExamplesConfig();
	return z.object({
		examples: z
			.array(
				z.object({
					EN: z.string().min(1, 'English example is required'),
					VI: z.string().min(1, 'Vietnamese example is required'),
				})
			)
			.min(1, 'At least one example is required')
			.max(
				examplesConfig.maxAddPerRequest,
				`Maximum ${examplesConfig.maxAddPerRequest} examples allowed`
			),
	});
}

export async function POST(
	request: NextRequest,
	{ params }: { params: Promise<{ wordId: string }> }
) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for adding examples.');

		const { wordId } = await params;
		const body = await request.json();

		// Use dynamic schema based on configuration
		const addExamplesSchema = await createAddExamplesSchema();
		const validatedData = addExamplesSchema.parse(body);
		const { examples } = validatedData;

		const wordService = await getWordService();
		const updatedWord = await wordService.addExamplesToWord(wordId, examples);

		return NextResponse.json({ word: updatedWord });
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Add examples error:', error);
		return NextResponse.json({ error: 'Failed to add examples' }, { status: 500 });
	}
}
