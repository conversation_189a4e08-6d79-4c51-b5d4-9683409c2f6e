import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { WordDetail } from '@/models';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

const vocabularyTranslateSchema = z.object({
	words: z
		.array(z.string())
		.min(1, 'At least one word is required')
		.max(10, 'Cannot translate more than 10 words at once'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for vocabulary translation.');

		const body = await request.json();
		const validatedData = vocabularyTranslateSchema.parse(body);
		const { words, source_language, target_language } = validatedData;

		// File-based cache disabled in development

		const llmService = await getLLMService();
		const translatedWords = await llmService.translateVocabulary(words, source_language, target_language);


		// File-based cache saving disabled in development

		return NextResponse.json(translatedWords);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Vocabulary translation error:', error);
		return NextResponse.json(
			{ error: 'Failed to translate vocabulary' },
			{ status: 500 }
		);
	}
}
