import { ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { Language } from '@prisma/client';
import { WordDetail } from '@/models';
import { NextRequest, NextResponse } from 'next/server';
import fs from 'node:fs/promises';
import path from 'node:path';
import { z, ZodError } from 'zod';

const generateWordDetailsSchema = z.object({
	terms: z
		.array(z.string())
		.min(1, 'At least one term is required')
		.max(20, 'Cannot generate details for more than 20 terms at once'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const validatedData = generateWordDetailsSchema.parse(body);
		const { terms, source_language, target_language } = validatedData;

		// File-based cache disabled in development

		const llmService = await getLLMService();
		const words = await llmService.generateWordDetails(terms, source_language, target_language);

		// File-based cache saving disabled in development

		return NextResponse.json(words);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		console.error('Error in generateWordDetailsApi:', error);
		return NextResponse.json(
			{ error: 'Failed to generate word details. Please try again.' },
			{ status: 500 }
		);
	}
}
