import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { TranslationEvaluationResult } from '@/backend/services';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

const evaluateTranslationSchema = z.object({
	original_text: z.string().min(1, 'Original text is required'),
	translated_text: z.string().min(1, 'User translation is required'),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

export async function POST(request: NextRequest) {
	let userId: string | undefined;
	try {
		const session = await auth();
		userId = session?.user?.id;
		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to evaluate translation.'
			);
		}

		const body = await request.json();
		const validatedData = evaluateTranslationSchema.parse(body);
		const { original_text, translated_text, source_language, target_language } = validatedData;

		const llmService = await getLLMService();
		const evaluation = await llmService.evaluateTranslation({
			original_text,
			translated_text,
			source_language,
			target_language,
		});

		return NextResponse.json(evaluation);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error(`Error in evaluateTranslationApi for user ${userId || 'unknown'}:`, error);
		return NextResponse.json(
			{ error: 'Failed to evaluate translation. Please try again.' },
			{ status: 500 }
		);
	}
}
