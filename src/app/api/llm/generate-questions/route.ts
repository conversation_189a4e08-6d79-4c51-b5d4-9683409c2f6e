import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

const generateQuestionsSchema = z.object({
	paragraph: z.string().min(1, 'Paragraph is required'),
	language: z.nativeEnum(Language),
	questionCount: z
		.number()
		.min(1, 'Question count must be at least 1')
		.max(10, 'Cannot generate more than 10 questions'),
});

export async function POST(request: NextRequest) {
	let userId: string | undefined;
	try {
		const session = await auth();
		userId = session?.user?.id;
		if (!userId) {
			throw new UnauthorizedError(
				'Unauthorized: User must be authenticated to generate questions.'
			);
		}

		const body = await request.json();
		const validatedData = generateQuestionsSchema.parse(body);
		const { paragraph, language, questionCount } = validatedData;

		const llmService = await getLLMService();
		const questions = await llmService.generateQuestions({
			paragraph,
			language,
			questionCount,
		});

		return NextResponse.json(questions);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error(`Error in generateQuestionsApi for user ${userId || 'unknown'}:`, error);
		return NextResponse.json(
			{ error: 'Failed to generate questions. Please try again.' },
			{ status: 500 }
		);
	}
}
