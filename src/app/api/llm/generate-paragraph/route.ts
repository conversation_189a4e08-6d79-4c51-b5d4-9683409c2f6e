import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { Difficulty, Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

const generateParagraphSchema = z.object({
	keywords: z
		.array(z.string())
		.min(1, 'At least one keyword is required')
		.max(10, 'Cannot use more than 10 keywords'),
	language: z.nativeEnum(Language),
	difficulty: z.nativeEnum(Difficulty),
	count: z
		.number()
		.min(1, 'Count must be at least 1')
		.max(5, 'Cannot generate more than 5 paragraphs at once'),
	sentenceCount: z.number().min(1).max(30).optional(),
});

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId)
			throw new UnauthorizedError('User not authenticated for generating paragraphs.');

		const body = await request.json();
		const validatedData = generateParagraphSchema.parse(body);
		const { keywords, language, difficulty, count, sentenceCount } = validatedData;

		const llmService = await getLLMService();
		const paragraphs = await llmService.generateParagraph({
			keywords,
			language,
			difficulty,
			count,
			sentenceCount,
		});

		return NextResponse.json(paragraphs);
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Error in generateParagraphApi:', error);
		return NextResponse.json(
			{ error: 'Failed to generate paragraph. Please try again.' },
			{ status: 500 }
		);
	}
}
