import { UnauthorizedError, ValidationError } from '@/backend/errors';
import { getLLMService } from '@/backend/wire';
import { auth } from '@/lib';
import { RandomWord } from '@/models';
import { Language } from '@prisma/client';
import { NextRequest, NextResponse } from 'next/server';
import { z, ZodError } from 'zod';

const generateRandomWordsSchema = z.object({
	keywordTerms: z.array(z.string()).min(1, 'At least one keyword term is required'),
	maxTerms: z.number().min(1).max(50),
	excludeCollectionIds: z.array(z.string()).optional().default([]),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
});

const generateRandomWordsPaginatedSchema = z.object({
	keywordTerms: z.array(z.string()).min(1, 'At least one keyword term is required'),
	maxTerms: z.number().min(1).max(50),
	excludeCollectionIds: z.array(z.string()).optional().default([]),
	excludeTerms: z.array(z.string()).optional().default([]),
	source_language: z.nativeEnum(Language),
	target_language: z.nativeEnum(Language),
	page: z.number().min(1).optional().default(1),
	pageSize: z.number().min(1).max(50).optional().default(10),
});

export async function POST(request: NextRequest) {
	try {
		const session = await auth();
		const userId = session?.user?.id;
		if (!userId) throw new UnauthorizedError('User not authenticated for generating words.');

		const body = await request.json();
		const { paginated, ...params } = body;

		if (paginated) {
			// Handle paginated request
			const validatedData = generateRandomWordsPaginatedSchema.parse(params);
			const {
				keywordTerms,
				maxTerms,
				excludeCollectionIds,
				excludeTerms,
				source_language,
				target_language,
				page,
				pageSize,
			} = validatedData;

			const llmService = await getLLMService();
			const result = await llmService.generateRandomTerms({
				userId,
				keywordTerms,
				excludesTerms: excludeTerms,
				maxTerms,
				excludeCollectionIds,
				source_language,
				target_language,
			});

			return NextResponse.json(result);
		} else {
			// Handle regular request
			const validatedData = generateRandomWordsSchema.parse(params);
			const {
				keywordTerms,
				maxTerms,
				excludeCollectionIds,
				source_language,
				target_language,
			} = validatedData;

			const llmService = await getLLMService();
			const words = await llmService.generateRandomTerms({
				userId,
				keywordTerms,
				excludesTerms: [],
				maxTerms,
				excludeCollectionIds,
				source_language,
				target_language,
			});

			return NextResponse.json(words as RandomWord[]);
		}
	} catch (error) {
		if (error instanceof ZodError) {
			return NextResponse.json({ error: error.errors[0].message }, { status: 400 });
		}

		if (error instanceof ValidationError) {
			return NextResponse.json({ error: error.message }, { status: 400 });
		}

		if (error instanceof UnauthorizedError) {
			return NextResponse.json({ error: error.message }, { status: 401 });
		}

		console.error('Error in generateRandomWordsApi:', error);
		return NextResponse.json(
			{ error: 'Failed to generate random words. Please try again.' },
			{ status: 500 }
		);
	}
}
