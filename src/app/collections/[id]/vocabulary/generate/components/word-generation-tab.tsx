'use client';

import { SuccessBanner } from '@/components/onboarding';
import { usePageGuidance } from '@/hooks/use-page-guidance';
import { useToast } from '@/contexts/toast-context';
import { useKeywordsContext, useTranslation } from '@/contexts';
import { useLLM } from '@/contexts/llm-context';
import { useCollections } from '@/hooks';
import { useUndoActions, createWordAddedAction } from '@/hooks/use-undo-actions';
import { useVocabularyGeneration } from '@/hooks/use-vocabulary-generation';

import { WordDetail } from '@/models';
import { useEffect, useState, useCallback } from 'react';
import { KeywordForm } from '../../../components/keyword-form';
import { PracticeSessionSkeleton } from '../../../components/practice-session-skeleton';
import { GenerationLoadingState } from '../../../paragraph/components/generation-loading-state';
import { WordList } from './word-list';
import { RecommendedPackages } from '@/components/word-packages/recommended-packages';
import { WordGenerationControls } from './word-generation-controls';

interface WordGenerationTabProps {
	collectionId: string;
	onSearchWordNetTerm?: (term: string) => void;
	wordNetSearchLoading?: Record<string, boolean>;
}

export function WordGenerationTab({
	collectionId,
	onSearchWordNetTerm,
	wordNetSearchLoading = {},
}: WordGenerationTabProps) {
	const { t } = useTranslation();
	const { showSuccess, showError } = useToast();
	const { generateWordDetails } = useLLM();

	const {
		currentCollection,
		loading: collectionsLoading,
		error: collectionsError,
		addTermToCurrentCollection,
		addWordsToCurrentCollection,
		removeWordsFromCurrentCollection,
		refreshCurrentCollection,
	} = useCollections();

	const {
		isLoading: isKeywordsLoading,
		selectedKeywords,
		keywords,
		error: keywordsError,
	} = useKeywordsContext();

	// Undo actions hook
	const { addUndoAction } = useUndoActions();

	// Vocabulary generation hook
	const {
		generatedWords,
		detailedWords,
		addedWords,
		isGenerating,
		isLoadingMore,
		sessionAddedCount,
		error: generationError,
		generateWords,
		loadMoreWords,
		addWordToCollection,
		removeWordFromCollection,
		setWordDetail,
		setWordLoadingState,
		getWordLoadingState,
	} = useVocabularyGeneration();

	// Success tracking
	const [showSuccessBanner, setShowSuccessBanner] = useState(false);
	const [initialWordCount, setInitialWordCount] = useState(0);

	// Check if any critical loading is happening
	const isCollectionsLoading = collectionsLoading.get || collectionsLoading.setCurrent;
	const isAnyLoading = isCollectionsLoading || isGenerating || isKeywordsLoading;
	const hasErrors = !!(collectionsError || keywordsError || generationError);

	// Page Guidance hook - must be called at the top level
	usePageGuidance({
		titleKey: 'words.guidance.combined.title',
		steps: [
			{ key: 'words.guidance.combined.step1' },
			{ key: 'words.guidance.combined.step2' },
			{ key: 'words.guidance.combined.step3' },
			{ key: 'words.guidance.combined.step4' },
		],
		tipKey: 'words.guidance.combined.tip',
		defaultOpen: (currentCollection?.word_ids?.length || 0) === 0,
	});

	// Initialize word count tracking
	useEffect(() => {
		if (currentCollection && initialWordCount === 0) {
			setInitialWordCount(currentCollection.word_ids.length);
		}
	}, [currentCollection, initialWordCount]);

	// Error notifications
	useEffect(() => {
		if (collectionsError) {
			showError(new Error(t('collections.error')));
		}
		if (keywordsError) {
			showError(new Error(t('keywords.error')));
		}
		if (generationError) {
			showError(new Error(generationError));
		}
	}, [collectionsError, keywordsError, generationError, t, showError]);

	// Handle generate button click
	const handleGenerate = useCallback(async () => {
		if (!currentCollection || selectedKeywords.length === 0 || isAnyLoading || hasErrors)
			return;

		const keywordNames = selectedKeywords
			.map((id) => {
				const keyword = keywords.find((k) => k.id === id);
				return keyword?.content || '';
			})
			.filter(Boolean);

		if (keywordNames.length === 0) {
			showError(new Error(t('keywords.error')));
			return;
		}

		try {
			await generateWords(
				currentCollection.id,
				keywordNames,
				currentCollection.source_language,
				currentCollection.target_language
			);
		} catch (error) {
			// Error is already handled by the hook
		}
	}, [
		currentCollection,
		selectedKeywords,
		isAnyLoading,
		hasErrors,
		keywords,
		generateWords,
		showError,
		t,
	]);

	// Handle load more words
	const handleLoadMore = useCallback(async () => {
		if (!currentCollection) return;

		try {
			await loadMoreWords(
				currentCollection.id,
				currentCollection.source_language,
				currentCollection.target_language
			);
		} catch (error) {
			// Error is already handled by the hook
		}
	}, [currentCollection, loadMoreWords]);

	// Get word details
	const handleGetDetails = useCallback(
		async (word: { term: string }) => {
			const loadingState = getWordLoadingState(word.term);
			if (loadingState.gettingDetail || detailedWords[word.term] || !currentCollection) {
				return;
			}

			setWordLoadingState(word.term, { gettingDetail: true });

			try {
				const detailsList = await generateWordDetails(
					[word.term],
					currentCollection.source_language,
					currentCollection.target_language
				);
				if (detailsList && detailsList.length > 0) {
					const wordDetail = detailsList[0] as WordDetail;
					setWordDetail(word.term, wordDetail);
				} else {
					throw new Error(t('words.detail_fetch_no_data', { term: word.term }));
				}
			} catch (error) {
				showError(new Error(t('words.detail_fetch_error')));
			} finally {
				setWordLoadingState(word.term, { gettingDetail: false });
			}
		},
		[
			getWordLoadingState,
			detailedWords,
			currentCollection,
			setWordLoadingState,
			generateWordDetails,
			setWordDetail,
			t,
			showError,
		]
	);

	// Generate additional examples for a word
	const handleGenerateExamples = useCallback(
		async (word: { term: string }) => {
			const loadingState = getWordLoadingState(word.term);
			if (loadingState.generatingExamples) return;

			setWordLoadingState(word.term, { generatingExamples: true });

			try {
				// Check if word exists in database with definitions
				const detailedWord = detailedWords[word.term];
				if (!detailedWord?.id || !detailedWord.definitions?.[0]?.id) {
					showError(
						new Error('Word must be saved to collection first before loading examples')
					);
					return;
				}

				// Generate new examples via API
				const response = await fetch(`/api/words/${detailedWord.id}/examples/generate`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({
						definitionId: detailedWord.definitions[0].id,
						count: 3,
					}),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to generate examples');
				}

				const result = await response.json();

				// Check if any new examples were generated
				if (result.examples.length === 0) {
					showError(
						new Error(
							'All generated examples already exist. No new examples were added.'
						)
					);
					return;
				}

				// Update the detailed word with new examples
				const updatedWord = {
					...detailedWord,
					definitions: detailedWord.definitions.map((def, index) => {
						if (index === 0) {
							// Append new examples to the first definition
							return {
								...def,
								examples: [...def.examples, ...result.examples],
							};
						}
						return def;
					}),
				};

				// Update the detailed words state
				setWordDetail(word.term, updatedWord);

				showSuccess(t('words.examples_generated', { count: result.count }));
			} catch (error) {
				const errorMessage =
					error instanceof Error ? error.message : 'Failed to generate examples';
				showError(new Error(errorMessage));
			} finally {
				setWordLoadingState(word.term, { generatingExamples: false });
			}
		},
		[
			getWordLoadingState,
			detailedWords,
			setWordLoadingState,
			setWordDetail,
			showSuccess,
			showError,
			t,
		]
	);

	// Add word to collection
	const handleAddToCollection = useCallback(
		async (word: { term: string }) => {
			if (!currentCollection) return;

			try {
				// Use the vocabulary generation hook's addWordToCollection which properly manages loading states
				const result = await addWordToCollection(
					currentCollection.id,
					word.term,
					currentCollection.target_language
				);

				// Add undo action if word was successfully added
				if (result) {
					// Find the newly added word ID by looking for the word with matching term
					const addedWord = result.words?.find((w) => w.term === word.term);
					if (addedWord?.id) {
						const undoAction = createWordAddedAction(
							{ term: word.term, id: addedWord.id },
							currentCollection.id,
							async (wordId: string) => {
								await removeWordsFromCurrentCollection([wordId]);
								await removeWordFromCollection(
									currentCollection.id,
									[wordId],
									word.term
								);
								await refreshCurrentCollection();
							}
						);
						addUndoAction(undoAction);
					}
				}

				// Update session tracking
				setShowSuccessBanner(true);

				// Show success toast
				showSuccess(
					`${t('words.word_added')} - ${t('words.word_added_desc', { term: word.term })}`
				);

				await refreshCurrentCollection();
			} catch (error) {
				showError(new Error(t('words.add_error')));
			}
		},
		[
			currentCollection,
			addWordToCollection,
			removeWordsFromCurrentCollection,
			removeWordFromCollection,
			refreshCurrentCollection,
			addUndoAction,
			showSuccess,
			showError,
			t,
		]
	);

	// Handle undo word addition
	const handleUndoWordAddition = useCallback(async (_word: { term: string }) => {
		// This will be handled by the undo action created in handleAddToCollection
		// The undo system will manage the actual removal
	}, []);

	// Show loading skeleton while any critical data is loading
	if (isAnyLoading && !currentCollection) {
		return <PracticeSessionSkeleton type="paragraph" />;
	}

	// Collection safety check
	if (!currentCollection) return null;

	const currentWordCount = currentCollection.word_ids.length;

	return (
		<div className="space-y-8">
			{/* Success Banner */}
			{showSuccessBanner && sessionAddedCount > 0 && (
				<SuccessBanner
					collectionId={currentCollection.id}
					addedWordsCount={sessionAddedCount}
					totalWordsInCollection={currentWordCount}
					onDismiss={() => setShowSuccessBanner(false)}
				/>
			)}

			<section className="space-y-4">
				<KeywordForm />

				{/* Generate button */}
				<WordGenerationControls
					onGenerate={handleGenerate}
					isGenerating={isGenerating}
					isLoading={isKeywordsLoading}
					hasErrors={hasErrors}
				/>

				{/* Recommended Word Packages */}
				{currentCollection && (
					<RecommendedPackages
						collectionId={currentCollection.id}
						source_language={currentCollection.source_language}
						target_language={currentCollection.target_language}
						className="mb-8"
					/>
				)}

				<div className="grid grid-cols-1 gap-4">
					{/* Show loading state for word list operations */}
					{isGenerating ? (
						<GenerationLoadingState
							titleKey="words.generating_please_wait"
							description="Creating personalized vocabulary for your learning journey..."
						/>
					) : (
						<div className="relative">
							<WordList
								words={generatedWords}
								detailedWords={detailedWords}
								onGetDetails={handleGetDetails}
								getLoadingState={getWordLoadingState}
								onAddToCollection={handleAddToCollection}
								onUndoWordAddition={handleUndoWordAddition}
								onGenerateExamples={handleGenerateExamples}
								addedWords={addedWords}
								className="mt-6"
								sourceLanguage={currentCollection.source_language}
								targetLanguage={currentCollection.target_language}
								isLoadingMore={isLoadingMore}
								onLoadMore={handleLoadMore}
								onSearchWordNetTerm={onSearchWordNetTerm}
								wordNetSearchLoading={wordNetSearchLoading}
							/>
						</div>
					)}
				</div>
			</section>
		</div>
	);
}
