'use client';

import { TikTokWordCard } from '@/app/collections/[id]/components/tiktok-word-card';
import { EmptyStateGuidance } from '@/components/onboarding';
import { But<PERSON>, LoadingSpinner, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useLoading } from '@/contexts/loading-context';
import { useToast } from '@/contexts/toast-context';
import { useCollections } from '@/hooks';
import { usePageGuidance } from '@/hooks/use-page-guidance';
import { motion } from 'framer-motion';
import { ArrowLeft, BookOpen, CheckCircle, RotateCcw } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useCallback, useEffect, useRef, useState } from 'react';
import { PracticeSessionSkeleton } from '../../components/practice-session-skeleton';

const REVIEW_LIMIT = 20; // Increased for better TikTok experience

export function TikTokReviewClient({ params }: { params: { id: string } }) {
	const collectionId = params.id;
	const { currentCollection, loading, error } = useCollections();
	const isLoading = loading.get || loading.setCurrent;
	const { t } = useTranslation();
	const { showSuccess, showError } = useToast();
	const router = useRouter();
	const isActive = true;

	const { setLoading: setGlobalLoading } = useLoading();
	const {
		currentCollectionWords: wordsToReview,
		loading: { getWordsToReviewWords: getWordsToReviewLoading },
		error: fetchError,
		getCurrentCollectionWordsToReview: getWordsToReview,
	} = useCollections();

	const [viewedWords, setViewedWords] = useState<Set<string>>(new Set());
	const [unlockedWords, setUnlockedWords] = useState<Set<string>>(new Set());
	const [currentWordIndex, setCurrentWordIndex] = useState(0);
	const [initialFetchDone, setInitialFetchDone] = useState(false);
	const scrollContainerRef = useRef<HTMLDivElement>(null);

	// Page Guidance hook
	usePageGuidance({
		titleKey: 'words.guidance.review.title',
		steps: [
			{ key: 'words.guidance.review.tiktok.step1' },
			{ key: 'words.guidance.review.tiktok.step2' },
			{ key: 'words.guidance.review.tiktok.step3' },
		],
		tipKey: 'words.guidance.review.tiktok.tip',
		defaultOpen: !currentCollection?.word_ids?.length,
	});

	// Reset state when collection changes
	useEffect(() => {
		setInitialFetchDone(false);
		setCurrentWordIndex(0);
		setViewedWords(new Set());
		setUnlockedWords(new Set());
	}, [collectionId]);

	// Initial fetch effect
	useEffect(() => {
		if (isActive && collectionId && !initialFetchDone && !getWordsToReviewLoading) {
			getWordsToReview(REVIEW_LIMIT);
			setInitialFetchDone(true);
		}
	}, [collectionId, isActive, initialFetchDone, getWordsToReviewLoading, getWordsToReview]);

	// Scroll to top when new words are loaded
	useEffect(() => {
		if (wordsToReview.length > 0 && !getWordsToReviewLoading && scrollContainerRef.current) {
			// Small delay to ensure DOM is updated
			setTimeout(() => {
				if (scrollContainerRef.current) {
					scrollContainerRef.current.scrollTo({
						top: 0,
						behavior: 'smooth',
					});
				}
			}, 100);
		}
	}, [wordsToReview.length, getWordsToReviewLoading]);

	// Global loading state
	useEffect(() => {
		if (isActive) {
			setGlobalLoading(getWordsToReviewLoading);
		} else {
			if (getWordsToReviewLoading) {
				setGlobalLoading(false);
			}
		}
	}, [getWordsToReviewLoading, setGlobalLoading, isActive]);

	const handleWordViewed = useCallback(
		async (wordId: string) => {
			if (viewedWords.has(wordId)) return;

			setViewedWords((prev) => new Set([...prev, wordId]));

			try {
				const response = await fetch('/api/last-seen-word', {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify({ wordId }),
				});

				if (!response.ok) {
					const errorData = await response.json();
					throw new Error(errorData.error || 'Failed to mark word as seen');
				}

				// Removed showSuccess toast to reduce notification noise
			} catch (error) {
				console.error('Failed to mark word as seen:', error);
				showError(new Error(t('review.mark_seen_error_title')));
			}
		},
		[viewedWords, showError, t]
	);

	const handleToggleVietnamese = useCallback((wordId: string) => {
		setUnlockedWords((prev) => {
			const newSet = new Set(prev);
			if (newSet.has(wordId)) {
				newSet.delete(wordId);
			} else {
				newSet.add(wordId);
			}
			return newSet;
		});
	}, []);

	const handleLoadMoreWords = useCallback(() => {
		if (collectionId && !getWordsToReviewLoading) {
			// Reset state when loading new words
			setViewedWords(new Set());
			setUnlockedWords(new Set());
			setCurrentWordIndex(0);
			getWordsToReview(REVIEW_LIMIT);
		}
	}, [collectionId, getWordsToReviewLoading, getWordsToReview]);

	const handleGenerateWords = useCallback(() => {
		if (collectionId) {
			router.push(`/collections/${collectionId}/vocabulary/generate`);
		}
	}, [collectionId, router]);

	const handleBack = useCallback(() => {
		router.back();
	}, [router]);

	if (!initialFetchDone && !isActive) {
		return <div className="py-4 text-center text-muted-foreground">{/* Inactive state */}</div>;
	}

	if (getWordsToReviewLoading && wordsToReview.length === 0 && isActive) {
		return <PracticeSessionSkeleton type="review" />;
	}

	if (fetchError && isActive) {
		return (
			<div className="container mx-auto py-8 text-center">
				<p className="text-destructive mb-4">
					{t('review.fetch_error_title')}:{' '}
					{fetchError?.message || t('review.unknown_error')}
				</p>
				<Button onClick={handleLoadMoreWords} variant="outline">
					<Translate text="review.try_refetch_btn" />
				</Button>
			</div>
		);
	}

	if (!currentCollection) return null;

	if (initialFetchDone && !getWordsToReviewLoading && wordsToReview.length === 0) {
		return (
			<div className="min-h-screen flex items-center justify-center">
				<EmptyStateGuidance
					titleKey="words.empty.review.title"
					descriptionKey="words.empty.review.description"
					actionKey="words.empty.review.action"
					actionHref={`/collections/${currentCollection.id}/vocabulary/generate`}
					icon={BookOpen}
				/>
			</div>
		);
	}

	return (
		<div className="fixed inset-0 bg-black overflow-hidden">
			{/* Floating Back Button */}
			<motion.div
				initial={{ opacity: 0, x: -50 }}
				animate={{ opacity: 1, x: 0 }}
				className="fixed top-4 left-4 z-50"
			>
				<Button
					variant="ghost"
					size="icon"
					onClick={handleBack}
					className="rounded-full bg-black/30 backdrop-blur-sm hover:bg-black/50 text-white border-white/20 border transition-all duration-200 hover:scale-110"
				>
					<ArrowLeft className="h-5 w-5" />
				</Button>
			</motion.div>

			{/* Main fullscreen scrollable container */}
			<div
				ref={scrollContainerRef}
				className="h-full w-full overflow-y-auto snap-y snap-mandatory scrollbar-hide"
				style={{
					scrollBehavior: 'smooth',
				}}
			>
				{wordsToReview.map((word, index) => (
					<TikTokWordCard
						key={word.id}
						word={word}
						sourceLanguage={currentCollection.source_language}
						targetLanguage={currentCollection.target_language}
						onWordViewed={handleWordViewed}
						showSourceLanguage={unlockedWords.has(word.id)}
						onToggleTargetLanguage={() => handleToggleVietnamese(word.id)}
						isActive={true}
					/>
				))}

				{/* Load more section */}
				{wordsToReview.length > 0 && (
					<div className="h-screen flex items-center justify-center snap-start snap-always bg-gradient-to-br from-green-900/20 via-blue-900/20 to-purple-900/20">
						<div className="text-center space-y-6 px-6">
							<motion.div
								initial={{ scale: 0 }}
								animate={{ scale: 1 }}
								transition={{ type: 'spring', duration: 0.8 }}
							>
								<CheckCircle className="mx-auto h-20 w-20 text-green-500" />
							</motion.div>
							<motion.h2
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: 0.2 }}
								className="text-4xl font-bold text-white"
							>
								<Translate text="review.session_complete_title" />
							</motion.h2>
							<motion.p
								initial={{ opacity: 0, y: 20 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: 0.4 }}
								className="text-gray-300 text-xl max-w-md mx-auto"
							>
								You&apos;ve viewed {viewedWords.size} out of {wordsToReview.length}{' '}
								words!
							</motion.p>
							<motion.div
								initial={{ opacity: 0, y: 30 }}
								animate={{ opacity: 1, y: 0 }}
								transition={{ delay: 0.6 }}
								className="flex flex-col gap-4"
							>
								<Button
									onClick={handleLoadMoreWords}
									disabled={getWordsToReviewLoading}
									size="lg"
									className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-full py-6 px-8 text-lg font-semibold transition-all duration-200 hover:scale-105"
								>
									{getWordsToReviewLoading ? (
										<LoadingSpinner size="sm" />
									) : (
										<>
											<RotateCcw className="mr-3 h-6 w-6" />
											<Translate text="review.reload_words" />
										</>
									)}
								</Button>
								<Button
									onClick={handleGenerateWords}
									variant="outline"
									size="lg"
									className="border-white/30 text-white hover:bg-white/10 rounded-full py-6 px-8 text-lg font-semibold transition-all duration-200 hover:scale-105"
								>
									<Translate text="words.generate_words" />
								</Button>
							</motion.div>
						</div>
					</div>
				)}
			</div>

			{/* Floating refresh button */}
			<motion.div
				initial={{ opacity: 0, scale: 0.8, y: 20 }}
				animate={{ opacity: 1, scale: 1, y: 0 }}
				transition={{ delay: 0.2 }}
				className="fixed bottom-6 right-6 z-50"
			>
				<Button
					onClick={handleLoadMoreWords}
					disabled={getWordsToReviewLoading}
					size="icon"
					className="rounded-full bg-black/30 backdrop-blur-sm hover:bg-black/50 text-white border-white/20 border transition-all duration-200 hover:scale-110"
				>
					{getWordsToReviewLoading ? (
						<LoadingSpinner size="sm" />
					) : (
						<RotateCcw className="h-5 w-5" />
					)}
				</Button>
			</motion.div>

			{/* Custom CSS for hiding scrollbar and smooth scrolling */}
			<style jsx global>{`
				.scrollbar-hide {
					-ms-overflow-style: none;
					scrollbar-width: none;
				}
				.scrollbar-hide::-webkit-scrollbar {
					display: none;
				}

				/* Enhanced snap scrolling */
				.snap-y {
					scroll-snap-type: y mandatory;
				}
				.snap-start {
					scroll-snap-align: start;
				}
				.snap-always {
					scroll-snap-stop: always;
				}

				/* Smooth scrolling behavior */
				@media (prefers-reduced-motion: no-preference) {
					.scrollbar-hide {
						scroll-behavior: smooth;
					}
				}

				/* Hide scrollbar completely and enable full touch scrolling */
				.scrollbar-hide {
					scrollbar-width: none;
					-webkit-overflow-scrolling: touch;
				}

				.scrollbar-hide::-webkit-scrollbar {
					width: 0;
					height: 0;
					display: none;
				}
			`}</style>
		</div>
	);
}
