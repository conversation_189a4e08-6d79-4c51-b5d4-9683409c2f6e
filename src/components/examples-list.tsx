'use client';

import { But<PERSON>, Translate } from '@/components/ui';
import { getTranslationKeyOfLanguage, formatRelativeTime } from '@/lib';
import { Language } from '@prisma/client';
import { ChevronDown, Loader2, Clock } from 'lucide-react';
import React, { memo } from 'react';
import { useWordExamples } from '@/hooks/use-word-examples';
import { getExamplesConstants } from '@/constants/examples';

interface Example {
	id?: string;
	EN: string;
	VI: string;
	created_at?: Date;
	updated_at?: Date;
}

interface Definition {
	id: string;
	examples: Example[];
}

interface ExamplesListProps {
	wordId: string;
	definition: Definition;
	sourceLanguage: Language;
	targetLanguage: Language;
	onGenerateExamples?: () => void;
	generatingExamples?: boolean;
}

function ExamplesListComponent({
	wordId,
	definition,
	sourceLanguage,
	targetLanguage,
	generatingExamples = false,
}: Omit<ExamplesListProps, 'onGenerateExamples'>) {
	const { exampleStates, loadMoreExamples, initializeExamples } = useWordExamples();
	const [maxExamples, setMaxExamples] = React.useState<number>(20); // Default fallback

	// Get example state directly from the states object to avoid function dependency
	const exampleState = exampleStates[definition.id] || {
		examples: [],
		initialExamplesCount: 0,
		loading: false,
		error: null,
		hasMore: true,
	};

	// Load examples configuration
	React.useEffect(() => {
		getExamplesConstants().then((config) => {
			setMaxExamples(config.maxExamples);
		});
	}, []);

	// Initialize examples state when component mounts or definition changes
	React.useEffect(() => {
		if (definition.examples.length > 0) {
			initializeExamples(definition.id, definition.examples);
		}
	}, [definition.id, definition.examples, initializeExamples]);

	// Combine initial examples with loaded examples, avoiding duplicates and maintaining chronological order
	const allExamples = React.useMemo(() => {
		const seenIds = new Set<string>();
		const seenContents = new Set<string>();
		const combined = [];

		// Helper function to normalize content for comparison
		const normalizeContent = (example: Example) =>
			`${example.EN.toLowerCase().trim()}|${example.VI.toLowerCase().trim()}`;

		// Combine all examples from both sources first
		const allSources = [...definition.examples, ...exampleState.examples];

		// Add all examples, skipping duplicates
		for (const example of allSources) {
			const contentKey = normalizeContent(example);

			if (example.id && !seenIds.has(example.id) && !seenContents.has(contentKey)) {
				seenIds.add(example.id);
				seenContents.add(contentKey);
				combined.push(example);
			} else if (!example.id && !seenContents.has(contentKey)) {
				// If no ID, check content only
				seenContents.add(contentKey);
				combined.push(example);
			}
		}

		// Sort by created_at in descending order (newest first) to maintain consistent chronological order
		return combined.sort((a, b) => {
			// Handle cases where created_at might be missing (shouldn't happen but safety first)
			const aTime = a.created_at ? new Date(a.created_at).getTime() : 0;
			const bTime = b.created_at ? new Date(b.created_at).getTime() : 0;
			return bTime - aTime; // Descending order (newest first)
		});
	}, [definition.examples, exampleState.examples]);

	const totalExamplesShown = allExamples.length;
	const isLoadingExamples = exampleState.loading || generatingExamples;

	// Use server-provided hasMore flag instead of frontend calculation
	const canLoadMore =
		wordId && // Only show if we have a valid wordId
		exampleState.hasMore && // Use server hasMore
		!isLoadingExamples;

	const handleLoadMore = async () => {
		if (!wordId) return;
		await loadMoreExamples(wordId, definition.id);
	};

	return (
		<div>
			<div className="flex items-center justify-between mb-1.5">
				<p className="text-sm font-semibold text-muted-foreground flex items-center gap-2">
					<Translate text="words.examples" />:
					{isLoadingExamples && (
						<span className="flex items-center gap-1 text-xs text-muted-foreground/70">
							<Loader2 className="h-3 w-3 animate-spin" />
							<Translate text="words.loading" />
						</span>
					)}
				</p>
			</div>

			<div className="space-y-2">
				{allExamples.length > 0 ? (
					<>
						{allExamples.map((example, exIndex) => (
							<div
								key={
									example.id ||
									`example-${exIndex}-${example.EN?.slice(
										0,
										20
									)}-${example.VI?.slice(0, 20)}`
								}
								className="mb-2 last:mb-0 pl-3 border-l-2 border-secondary/30 py-1"
							>
								<div className="flex items-center justify-between mb-1">
									<p className="text-xs font-medium text-muted-foreground tracking-wide">
										<Translate
											text={getTranslationKeyOfLanguage(targetLanguage)}
										/>
										:
									</p>
								</div>
								<p className="mb-1 text-sm text-foreground/95">
									{example[targetLanguage] || (
										<span className="italic opacity-70">
											<Translate text="words.example_not_provided" />
										</span>
									)}
								</p>
								<p className="text-xs font-medium text-muted-foreground tracking-wide">
									<Translate text={getTranslationKeyOfLanguage(sourceLanguage)} />
									:
								</p>
								<p className="text-sm text-foreground/95">
									{example[sourceLanguage] || (
										<span className="italic opacity-70">
											<Translate text="words.translation_not_provided" />
										</span>
									)}
								</p>
							</div>
						))}
					</>
				) : (
					<p className="text-sm text-muted-foreground italic mb-2">
						<Translate text="words.no_examples_available" />
					</p>
				)}

				{allExamples.length > 0 && (
					<div className="text-xs text-muted-foreground text-center mt-2 mb-2">
						<Translate text="words.showing_examples" /> {allExamples.length}/
						{maxExamples}
					</div>
				)}

				{/* Load More Button - Show when canLoadMore OR when loading examples */}
				{(canLoadMore || isLoadingExamples) && (
					<div className="flex justify-center mt-3">
						<Button
							variant="outline"
							size="sm"
							onClick={handleLoadMore}
							disabled={isLoadingExamples}
							className="h-8 px-3 text-xs"
						>
							{isLoadingExamples ? (
								<>
									<Loader2 className="h-3 w-3 animate-spin mr-1" />
									<Translate text="words.loading_examples" />
								</>
							) : (
								<>
									<ChevronDown className="h-3 w-3 mr-1" />
									<Translate text="words.load_more_examples" />
								</>
							)}
						</Button>
					</div>
				)}

				{/* Loading Skeleton for new examples */}
				{isLoadingExamples && (
					<div className="space-y-2 mt-2">
						{[1, 2, 3].map((i) => (
							<div key={`skeleton-${i}`} className="animate-pulse">
								<div className="pl-3 border-l-2 border-secondary/30 py-1">
									<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
									<div className="h-4 bg-muted-foreground/20 rounded mb-1 w-full"></div>
									<div className="h-3 bg-muted-foreground/20 rounded mb-1 w-12"></div>
									<div className="h-4 bg-muted-foreground/20 rounded w-3/4"></div>
								</div>
							</div>
						))}
					</div>
				)}

				{/* Error Message */}
				{exampleState.error && (
					<div className="text-xs text-destructive text-center mt-2">
						{exampleState.error}
					</div>
				)}
			</div>
		</div>
	);
}

export const ExamplesList = memo(ExamplesListComponent);
