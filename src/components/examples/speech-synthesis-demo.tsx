'use client';

import { <PERSON><PERSON>, <PERSON>, <PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui';
import { useSpeechSynthesis } from '@/hooks';
import { Language } from '@prisma/client';
import { Volume2, VolumeX } from 'lucide-react';
import { useState } from 'react';

/**
 * Demo component showcasing speech synthesis functionality
 * This component demonstrates how to use the useSpeechSynthesis hook
 */
export function SpeechSynthesisDemo() {
	const { speak, stop, isSupported, isSpeaking } = useSpeechSynthesis();
	const [selectedLanguage, setSelectedLanguage] = useState<Language>(Language.EN);

	const demoTexts = {
		[Language.EN]: [
			'Hello, welcome to the vocabulary learning app!',
			'This is a demonstration of text-to-speech functionality.',
			'You can hear pronunciations in English.',
		],
		[Language.VI]: [
			'Xin chào, chào mừng bạn đến với ứng dụng học từ vựng!',
			'<PERSON><PERSON><PERSON> là minh họa chức năng chuyển văn bản thành giọng nói.',
			'<PERSON><PERSON><PERSON> có thể nghe phát âm bằng tiếng Việt.',
		],
	};

	const handleSpeak = (text: string) => {
		speak(text, selectedLanguage);
	};

	const handleStop = () => {
		stop();
	};

	if (!isSupported) {
		return (
			<Card className="w-full max-w-2xl mx-auto">
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<VolumeX className="h-5 w-5 text-muted-foreground" />
						Speech Synthesis Not Supported
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-muted-foreground">
						Your browser does not support the Web Speech API. Please try using a modern browser
						like Chrome, Safari, or Edge for the best experience.
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full max-w-2xl mx-auto">
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Volume2 className="h-5 w-5 text-primary" />
					Speech Synthesis Demo
				</CardTitle>
			</CardHeader>
			<CardContent className="space-y-6">
				{/* Language Selection */}
				<div className="space-y-2">
					<label className="text-sm font-medium">Select Language:</label>
					<div className="flex gap-2">
						<Button
							variant={selectedLanguage === Language.EN ? 'default' : 'outline'}
							size="sm"
							onClick={() => setSelectedLanguage(Language.EN)}
						>
							English
						</Button>
						<Button
							variant={selectedLanguage === Language.VI ? 'default' : 'outline'}
							size="sm"
							onClick={() => setSelectedLanguage(Language.VI)}
						>
							Vietnamese
						</Button>
					</div>
				</div>

				{/* Demo Texts */}
				<div className="space-y-3">
					<label className="text-sm font-medium">Demo Texts:</label>
					{demoTexts[selectedLanguage].map((text, index) => (
						<div
							key={index}
							className="flex items-start gap-3 p-3 rounded-lg border bg-accent/25"
						>
							<p className="flex-1 text-sm">{text}</p>
							<Button
								variant="ghost"
								size="sm"
								onClick={() => handleSpeak(text)}
								className="h-8 w-8 p-0 hover:bg-primary/10 transition-colors flex-shrink-0"
								title={`Speak: "${text}"`}
							>
								<Volume2 className="h-4 w-4 text-primary" />
							</Button>
						</div>
					))}
				</div>

				{/* Controls */}
				<div className="flex gap-2 pt-4 border-t">
					<Button
						variant="outline"
						size="sm"
						onClick={handleStop}
						disabled={!isSpeaking}
						className="flex items-center gap-2"
					>
						<VolumeX className="h-4 w-4" />
						Stop Speech
					</Button>
					<div className="flex-1" />
					<div className="text-xs text-muted-foreground self-center">
						{isSpeaking ? 'Speaking...' : 'Ready'}
					</div>
				</div>

				{/* Usage Information */}
				<div className="text-xs text-muted-foreground space-y-1 pt-4 border-t">
					<p>
						<strong>Usage:</strong> Click the volume icon next to any text to hear it spoken
						aloud.
					</p>
					<p>
						<strong>Languages:</strong> Supports English (en-US) and Vietnamese (vi-VN)
						pronunciation.
					</p>
					<p>
						<strong>Browser Support:</strong> Works best in Chrome, Safari, and Edge browsers.
					</p>
				</div>
			</CardContent>
		</Card>
	);
}
