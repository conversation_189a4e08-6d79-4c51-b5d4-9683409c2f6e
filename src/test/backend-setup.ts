// Backend test setup - simplified for Node.js environment

// Load test environment variables
import dotenv from 'dotenv';
dotenv.config({ path: '.env.test' });

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = 'postgresql://postgres:password@localhost:5432/vocab_test?schema=public';
process.env.JWT_SECRET = 'test-jwt-secret-key-for-testing-only';
process.env.LLM_OPENAI_API_KEY = 'test-openai-key';
process.env.MOCK_LLM_RESPONSES = 'true';
process.env.DISABLE_EXTERNAL_APIS = 'true';

// Mock Next.js modules that are not available in Node.js
jest.mock('next/navigation', () => ({
	useRouter: () => ({
		push: jest.fn(),
		replace: jest.fn(),
		back: jest.fn(),
		forward: jest.fn(),
		refresh: jest.fn(),
		prefetch: jest.fn(),
	}),
	useSearchParams: () => new URLSearchParams(),
	usePathname: () => '/',
	useParams: () => ({}),
	notFound: jest.fn(),
	redirect: jest.fn(),
}));

// Mock auth
jest.mock('@/lib/auth', () => ({
	auth: jest.fn().mockResolvedValue({
		user: { id: 'test-user-id', email: '<EMAIL>' },
	}),
}));

// Mock config
jest.mock('@/config', () => ({
	getLLMConfig: jest.fn().mockResolvedValue({
		openAIKey: 'test-openai-key',
		openAIModel: 'gpt-4o-mini',
		maxExamples: 8,
		temperature: 0.7,
		maxTokens: 1000,
	}),
	getLLMOptimizationConfig: jest.fn().mockResolvedValue({
		enabled: false,
		promptOptimization: { enabled: false, compressionTarget: 0.4 },
		caching: {
			enabled: false,
			ttl: {
				vocabulary: 604800,
				wordDetails: 604800,
				paragraphs: 259200,
				questions: 259200,
				evaluations: 2592000,
				grammarPractice: 86400,
			},
			semanticSimilarity: {
				enabled: false,
				threshold: 0.8,
				maxKeywords: 20,
				keywordWeight: 0.4,
				structuralWeight: 0.3,
				semanticWeight: 0.3,
			},
		},
		batchProcessing: {
			enabled: false,
			maxBatchSize: {
				generateWordDetails: 20,
				evaluateAnswers: 10,
				evaluateTranslation: 15,
				generateQuestions: 5,
				generateParagraph: 3,
				generateGrammarPractice: 2,
			},
			maxWaitTime: 2000,
			maxTokensPerBatch: 8000,
		},
		modelSelection: {
			enabled: false,
			costOptimization: false,
			qualityThreshold: 0.8,
			latencyThreshold: 5000,
			adaptiveLearning: false,
		},
		tokenManagement: {
			budgetLimits: { daily: 100000, monthly: 2500000 },
			costAlerts: { dailyThreshold: 10.0, monthlyThreshold: 250.0 },
			estimation: { enabled: false, trackActualUsage: false },
		},
		monitoring: {
			enabled: false,
			logLevel: 'info',
			metricsCollection: false,
		},
	}),
	getServerConfig: jest.fn().mockReturnValue({
		port: 3000,
		env: 'test',
	}),
	getAuthConfig: jest.fn().mockReturnValue({
		jwtSecret: 'test-jwt-secret',
		jwtExpiresIn: '1h',
	}),
}));

// Mock Prisma
jest.mock('@prisma/client', () => ({
	PrismaClient: jest.fn().mockImplementation(() => ({
		$connect: jest.fn(),
		$disconnect: jest.fn(),
		collection: {
			findMany: jest.fn(),
			findUnique: jest.fn(),
			create: jest.fn(),
			update: jest.fn(),
			delete: jest.fn(),
		},
		word: {
			findMany: jest.fn(),
			findUnique: jest.fn(),
			create: jest.fn(),
			update: jest.fn(),
			delete: jest.fn(),
		},
		user: {
			findMany: jest.fn(),
			findUnique: jest.fn(),
			create: jest.fn(),
			update: jest.fn(),
			delete: jest.fn(),
		},
	})),
	Language: {
		EN: 'EN',
		VI: 'VI',
	},
	Difficulty: {
		BEGINNER: 'BEGINNER',
		INTERMEDIATE: 'INTERMEDIATE',
		ADVANCED: 'ADVANCED',
	},
	PartsOfSpeech: {
		NOUN: 'NOUN',
		VERB: 'VERB',
		ADJECTIVE: 'ADJECTIVE',
		ADVERB: 'ADVERB',
		PREPOSITION: 'PREPOSITION',
		CONJUNCTION: 'CONJUNCTION',
		INTERJECTION: 'INTERJECTION',
		PRONOUN: 'PRONOUN',
		DETERMINER: 'DETERMINER',
		NUMERAL: 'NUMERAL',
	},
	Provider: {
		TELEGRAM: 'TELEGRAM',
		GOOGLE: 'GOOGLE',
		USERNAME_PASSWORD: 'USERNAME_PASSWORD',
	},
}));

// Mock OpenAI
jest.mock('openai', () => ({
	__esModule: true,
	default: jest.fn().mockImplementation(() => ({
		chat: {
			completions: {
				create: jest.fn().mockResolvedValue({
					choices: [
						{
							message: {
								content: JSON.stringify({
									words: [
										{
											term: 'test',
											language: 'EN',
											definitions: [
												{
													pos: ['noun'],
													ipa: '/test/',
													explains: {
														EN: 'A test',
														VI: 'Một bài kiểm tra',
													},
													examples: {
														EN: 'This is a test',
														VI: 'Đây là một bài kiểm tra',
													},
												},
											],
										},
									],
								}),
							},
						},
					],
					usage: { total_tokens: 100 },
				}),
			},
		},
	})),
}));

// Mock cache services
jest.mock('@/backend/services/semantic-cache.service', () => ({
	semanticCache: {
		getWithSemantic: jest.fn(),
		setWithSemantic: jest.fn(),
		generateLLMKey: jest.fn(),
	},
}));

jest.mock('@/backend/services/token-monitor.service', () => ({
	tokenMonitor: {
		trackUsage: jest.fn(),
		checkBudget: jest.fn().mockResolvedValue(true),
	},
}));

// Mock wire dependencies
jest.mock('@/backend/wire', () => ({
	getCollectionService: jest.fn(),
	getCollectionRepository: jest.fn(),
	getWordService: jest.fn(),
	getLLMService: jest.fn(),
	getUserService: jest.fn(),
	getCollectionStatsService: jest.fn(),
}));

console.log('✅ Backend test environment setup completed');
