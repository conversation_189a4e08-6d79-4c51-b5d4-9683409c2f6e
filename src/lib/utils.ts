'use client';

import { clsx, type ClassValue } from 'clsx';
import { twMerge } from 'tailwind-merge';

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

// Theme handling utils
export type Theme = 'light' | 'dark' | 'system';

export function getSystemTheme(): Theme {
	if (typeof window !== 'undefined') {
		return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
	}
	return 'light'; // Default to light if SSR
}

// Function to set the theme in localStorage and apply it to the document
export function setTheme(theme: Theme) {
	if (typeof window === 'undefined') return;

	// Save theme preference
	localStorage.setItem('theme', theme);

	// Apply theme
	const isDark = theme === 'dark' || (theme === 'system' && getSystemTheme() === 'dark');

	document.documentElement.classList.toggle('dark', isDark);
}

// Function to get the current theme from localStorage
export function getTheme(): Theme {
	if (typeof window === 'undefined') return 'system';
	return (localStorage.getItem('theme') as Theme) || 'system';
}

// Date formatting utilities
export function formatRelativeTime(date: Date | string): string {
	const now = new Date();
	const targetDate = typeof date === 'string' ? new Date(date) : date;
	const diffInSeconds = Math.floor((now.getTime() - targetDate.getTime()) / 1000);

	if (diffInSeconds < 60) {
		return 'just now';
	} else if (diffInSeconds < 3600) {
		const minutes = Math.floor(diffInSeconds / 60);
		return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
	} else if (diffInSeconds < 86400) {
		const hours = Math.floor(diffInSeconds / 3600);
		return `${hours} hour${hours > 1 ? 's' : ''} ago`;
	} else if (diffInSeconds < 604800) {
		const days = Math.floor(diffInSeconds / 86400);
		return `${days} day${days > 1 ? 's' : ''} ago`;
	} else {
		return targetDate.toLocaleDateString();
	}
}
