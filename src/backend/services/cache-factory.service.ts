import { CacheService, CacheServiceImpl } from './cache.service';
import { RedisCacheService } from './redis-cache.service';

// Cache interface that both implementations must follow
export interface ICacheService {
	get<T>(key: string): Promise<T | null> | T | null;
	getOptimized<T>(key: string): Promise<T | null> | T | null;
	set<T>(key: string, value: T, ttl?: number): Promise<boolean> | boolean;
	setOptimized<T>(key: string, value: T, options?: any): Promise<boolean> | boolean;
	del(key: string): Promise<boolean> | boolean;
	flush(): Promise<void> | void;
	getStats(): Promise<any> | any;
	invalidateByTag(tag: string): Promise<number> | number;
	generateLLMKey(operation: string, params: Record<string, any>): string;
	generateKey(prefix: string, params: Record<string, any>): string;
}

// Wrapper to make node-cache async-compatible
export class AsyncCacheServiceWrapper implements ICacheService {
	constructor(private cacheService: CacheServiceImpl) {}

	async get<T>(key: string): Promise<T | null> {
		return this.cacheService.get<T>(key);
	}

	async getOptimized<T>(key: string): Promise<T | null> {
		return this.cacheService.getOptimized<T>(key);
	}

	async set<T>(key: string, value: T, ttl?: number): Promise<boolean> {
		return this.cacheService.set<T>(key, value, ttl);
	}

	async setOptimized<T>(key: string, value: T, options?: any): Promise<boolean> {
		return this.cacheService.setOptimized<T>(key, value, options);
	}

	async del(key: string): Promise<boolean> {
		return this.cacheService.del(key);
	}

	async flush(): Promise<void> {
		return this.cacheService.flush();
	}

	async getStats(): Promise<any> {
		return this.cacheService.getStats();
	}

	async invalidateByTag(tag: string): Promise<number> {
		return this.cacheService.invalidateByTag(tag);
	}

	generateLLMKey(operation: string, params: Record<string, any>): string {
		return this.cacheService.generateLLMKey(operation, params);
	}

	generateKey(prefix: string, params: Record<string, any>): string {
		return this.cacheService.generateKey(prefix, params);
	}
}

export class CacheFactory {
	private static instance: ICacheService | null = null;

	static async createCacheService(): Promise<ICacheService> {
		// Ensure this only runs on server side
		if (typeof window !== 'undefined') {
			throw new Error('Cache services can only be initialized on the server side');
		}

		if (this.instance) {
			return this.instance;
		}

		const useRedis =
			process.env.CACHE_PROVIDER === 'redis' || process.env.NODE_ENV === 'production';

		if (useRedis) {
			try {
				console.log('Initializing Redis cache service...');
				const redisCacheService = new RedisCacheService();
				// Test Redis connection
				await redisCacheService.get('test-connection');
				this.instance = redisCacheService;
				console.log('Redis cache service initialized successfully');
			} catch (error) {
				console.warn(
					'Failed to initialize Redis cache, falling back to node-cache:',
					error
				);
				this.instance = new AsyncCacheServiceWrapper(new CacheServiceImpl());
			}
		} else {
			console.log('Using node-cache service');
			this.instance = new AsyncCacheServiceWrapper(new CacheServiceImpl());
		}

		return this.instance;
	}

	static getInstance(): ICacheService | null {
		return this.instance;
	}

	static async resetInstance(): Promise<void> {
		if (this.instance && 'close' in this.instance) {
			await (this.instance as any).close();
		}
		this.instance = null;
	}
}

// Export singleton instances
export const getCacheService = async (): Promise<ICacheService> => {
	// Ensure this only runs on server side
	if (typeof window !== 'undefined') {
		throw new Error('Cache services can only be accessed on the server side');
	}
	return CacheFactory.createCacheService();
};
