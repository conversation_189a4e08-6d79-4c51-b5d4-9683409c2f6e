import { Language } from '@prisma/client';

export interface CacheStats {
	hits: number;
	misses: number;
	keys: number;
	memory: number;
	uptime: number;
	hitRate: number;
	memoryUsage: number;
}

export interface OptimizedCacheOptions {
	priority?: 'high' | 'normal' | 'low';
	tags?: string[];
	contentType?: string;
	ttl?: number;
}

export interface CacheService {
	// Example caching
	getCachedExamples(definitionId: string): Promise<Array<{
		id: string;
		EN: string;
		VI: string;
		created_at: Date;
		updated_at: Date;
	}> | null>;
	setCachedExamples(
		definitionId: string,
		examples: Array<{ id: string; EN: string; VI: string; created_at: Date; updated_at: Date }>,
		ttl?: number
	): Promise<boolean>;
	invalidateExamplesCache(definitionId: string): Promise<boolean>;

	// Word caching
	getCachedWord(wordId: string): Promise<any | null>;
	setCachedWord(wordId: string, word: any, ttl?: number): Promise<boolean>;
	invalidateWordCache(wordId: string): Promise<boolean>;

	// General cache operations (compatible with ICacheService)
	get<T>(key: string): Promise<T | null>;
	getOptimized<T>(key: string): Promise<T | null>;
	set<T>(key: string, value: T, ttl?: number): Promise<boolean>;
	setOptimized<T>(key: string, value: T, options?: any): Promise<boolean>;
	del(key: string): Promise<boolean>;
	flush(): Promise<void>;
	getStats(): Promise<any>;
	invalidateByTag(tag: string): Promise<number>;
	generateLLMKey(operation: string, params: Record<string, any>): string;
	generateKey(prefix: string, params: Record<string, any>): string;
}

interface CacheEntry<T> {
	value: T;
	expiresAt: number;
}

export class CacheServiceImpl implements CacheService {
	private cache = new Map<string, CacheEntry<any>>();
	private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

	async getCachedExamples(definitionId: string): Promise<Array<{
		id: string;
		EN: string;
		VI: string;
		created_at: Date;
		updated_at: Date;
	}> | null> {
		const key = `examples:${definitionId}`;
		return this.get(key);
	}

	async setCachedExamples(
		definitionId: string,
		examples: Array<{ id: string; EN: string; VI: string; created_at: Date; updated_at: Date }>,
		ttl: number = this.DEFAULT_TTL
	): Promise<boolean> {
		const key = `examples:${definitionId}`;
		return await this.set(key, examples, ttl);
	}

	async invalidateExamplesCache(definitionId: string): Promise<boolean> {
		const key = `examples:${definitionId}`;
		return await this.del(key);
	}

	async getCachedWord(wordId: string): Promise<any | null> {
		const key = `word:${wordId}`;
		return this.get(key);
	}

	async setCachedWord(
		wordId: string,
		word: any,
		ttl: number = this.DEFAULT_TTL
	): Promise<boolean> {
		const key = `word:${wordId}`;
		return await this.set(key, word, ttl);
	}

	async invalidateWordCache(wordId: string): Promise<boolean> {
		const key = `word:${wordId}`;
		return await this.del(key);
	}

	async get<T>(key: string): Promise<T | null> {
		const entry = this.cache.get(key);
		if (!entry) {
			return null;
		}

		// Check if entry has expired
		if (Date.now() > entry.expiresAt) {
			this.cache.delete(key);
			return null;
		}

		return entry.value as T;
	}

	async getOptimized<T>(key: string): Promise<T | null> {
		return this.get<T>(key);
	}

	async set<T>(key: string, value: T, ttl: number = this.DEFAULT_TTL): Promise<boolean> {
		const expiresAt = Date.now() + ttl;
		this.cache.set(key, { value, expiresAt });
		return true;
	}

	async setOptimized<T>(key: string, value: T, options?: any): Promise<boolean> {
		const ttl = options?.ttl || this.DEFAULT_TTL;
		return this.set(key, value, ttl);
	}

	async del(key: string): Promise<boolean> {
		return this.cache.delete(key);
	}

	async flush(): Promise<void> {
		this.cache.clear();
	}

	async getStats(): Promise<any> {
		return {
			keys: this.cache.size,
			hits: 0, // Not implemented in this simple cache
			misses: 0,
		};
	}

	async invalidateByTag(tag: string): Promise<number> {
		// Simple implementation - delete all keys containing the tag
		let deletedCount = 0;
		for (const key of this.cache.keys()) {
			if (key.includes(tag)) {
				this.cache.delete(key);
				deletedCount++;
			}
		}
		return deletedCount;
	}

	generateLLMKey(operation: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				result[key] = params[key];
				return result;
			}, {});
		return `llm:${operation}:${JSON.stringify(sortedParams)}`;
	}

	generateKey(prefix: string, params: Record<string, any>): string {
		const sortedParams = Object.keys(params)
			.sort()
			.reduce((result: Record<string, any>, key) => {
				result[key] = params[key];
				return result;
			}, {});
		return `${prefix}:${JSON.stringify(sortedParams)}`;
	}

	// Cleanup expired entries periodically
	private cleanupExpiredEntries(): void {
		const now = Date.now();
		for (const [key, entry] of this.cache.entries()) {
			if (now > entry.expiresAt) {
				this.cache.delete(key);
			}
		}
	}

	constructor() {
		// Cleanup expired entries every minute
		setInterval(() => {
			this.cleanupExpiredEntries();
		}, 60 * 1000);
	}
}
