// ============================================================================
// EXAMPLES CONSTANTS
// ============================================================================
// Constants related to word examples functionality
// Note: These are now loaded from configuration. Use getExamplesConfig() for server-side access.

import { getExamplesConfig } from '@/config';

// Cache for examples configuration to avoid repeated async calls
let examplesConfigCache: Awaited<ReturnType<typeof getExamplesConfig>> | null = null;

/**
 * Get examples configuration with caching
 * This is a helper function for client-side components that need synchronous access
 */
export async function getExamplesConstants() {
	if (!examplesConfigCache) {
		examplesConfigCache = await getExamplesConfig();
	}
	return examplesConfigCache;
}

/**
 * Maximum number of examples that can be loaded for a word definition
 * @deprecated Use getExamplesConfig().maxExamples instead
 */
export const MAX_EXAMPLES = 20;

/**
 * Default number of examples to load per request
 * @deprecated Use getExamplesConfig().defaultLoadLimit instead
 */
export const EXAMPLES_LOAD_LIMIT = 3;

/**
 * Initial number of examples to show when displaying a word
 * @deprecated Use getExamplesConfig().initialDisplayCount instead
 */
export const INITIAL_EXAMPLES_COUNT = 5;
